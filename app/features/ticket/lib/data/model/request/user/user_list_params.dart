import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_list_params.freezed.dart';
part 'user_list_params.g.dart';

@freezed
class UserListParams with _$UserListParams {
  const factory UserListParams({
    @Default('') String search,
    @Default(30) int limit,
    @Default('') String nextLink,
    @Default(true) bool onlyCurrentWorkspace,
    @Default(false) bool isSearching,
    @Default('') String groupId,
    @Default('') String threadId,
    @Default([]) List<int> filterOutMemberIds,
    @Default(false) bool ignoreMe,
  }) = _UserListParams;

  factory UserListParams.fromJson(Map<String, dynamic> json) =>
      _$UserListParamsFromJson(json);
}
