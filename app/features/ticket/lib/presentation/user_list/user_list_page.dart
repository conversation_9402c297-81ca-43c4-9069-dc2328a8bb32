import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/presentation/base/base.dart';

import 'bloc/user_list_bloc.dart';
import 'widgets/user_list_view.dart';

class UserListPage extends StatefulWidget {
  const UserListPage({
    this.title,
    this.filterOutMemberIds = const [],
    this.ignoreMe = false,
    this.onUserSelected,
    super.key,
  });

  final String? title;
  final List<int> filterOutMemberIds;
  final bool ignoreMe;
  final Function(Assignee)? onUserSelected;

  @override
  State<UserListPage> createState() => _UserListPageState();
}

class _UserListPageState extends State<UserListPage> {
  final UserListBloc bloc = UserListBloc();
  final TextEditingController textController = TextEditingController();
  String currentSearchText = '';

  Timer? _debounce;

  late GPBaseListViewParamsV2 defaultListParams = GPBaseListViewParamsV2(
    listBloc: bloc,
    inputData: '',
    needLoadDataWhenInitial: true,
    onRefresh: () {
      loadData();
    },
  );

  @override
  void initState() {
    super.initState();

    // Set filter options for bloc
    bloc.setFilterOptions(
      filterOutMemberIds: widget.filterOutMemberIds,
      ignoreMe: widget.ignoreMe,
    );

    textController.addListener(_onSearchTextChanged);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    textController.dispose();
    bloc.close();
    super.dispose();
  }

  void _onClose() {
    if (!mounted) return;
    Navigator.of(context).maybePop();
  }

  void _onSearchTextChanged() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    _debounce = Timer(const Duration(milliseconds: 300), () {
      loadData();
    });
  }

  void loadData() {
    final text = textController.text.trim();
    currentSearchText = text;

    defaultListParams.inputData = text;

    if (text.isNotEmpty) {
      search(text);
    } else {
      load(text);
    }
  }

  void search(String searchText) {
    bloc.searchData(
      searchParams: searchText,
      inputData: searchText,
    );
  }

  void load(String searchText) {
    bloc.loadData(
      inputData: searchText,
    );
  }

  void _onUserSelected(Assignee user) {
    widget.onUserSelected?.call(user);
    _onClose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<GPBaseListBlocV2>(
      create: (context) => bloc,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.title ??
                LocaleKeys.ticket_details_actions_add_handler_title.tr,
            style: textStyle(GPTypography.headingMedium),
          ),
          leading: IconButton(
            icon: const SvgWidget(
              Assets.PACKAGES_GP_ASSETS_IMAGES_IC24_FILL_CHEVRON_LEFT_PNG,
              width: 24,
              height: 24,
            ),
            onPressed: _onClose,
          ),
        ),
        body: Column(
          children: [
            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: GPSearchBar(
                textEditingController: textController,
                hintText: LocaleKeys.memberPicker_assignee_search_text.tr,
                showClearBtn: true,
                showBorder: true,
              ),
            ),
            // Divider
            Divider(
              height: 1,
              color: GPColor.lineTertiary,
            ),
            // User List
            Expanded(
              child: UserListView(
                bloc: bloc,
                searchStr: textController.text,
                onUserSelected: _onUserSelected,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
