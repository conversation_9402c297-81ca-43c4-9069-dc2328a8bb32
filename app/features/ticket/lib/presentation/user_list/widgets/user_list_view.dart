import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/presentation/base/base.dart';

class UserListView extends StatelessWidget {
  const UserListView({
    required this.bloc,
    this.searchStr = '',
    this.onUserSelected,
    super.key,
  });

  final GPBaseListBlocV2 bloc;
  final String searchStr;
  final Function(Assignee)? onUserSelected;

  Widget _buildItem(BuildContext context, Assignee item, int index) {
    String? department;
    if (item.info?.work?.isNotEmpty ?? false) {
      department = item.info?.work?.first.title;
    }
    String? title;
    if (item.info?.work?.isNotEmpty ?? false) {
      title = item.info?.work?.first.department;
    }

    return Column(
      children: [
        InkWell(
          onTap: () => onUserSelected?.call(item),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                AssigneeAvatar(
                  assignee: item,
                  size: 40,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.displayNameUserMe,
                        style: textStyle(GPTypography.bodyMedium)
                            ?.copyWith(fontWeight: FontWeight.w500),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (title?.isNotEmpty == true ||
                          department?.isNotEmpty == true)
                        const SizedBox(height: 2),
                      if (title?.isNotEmpty == true)
                        Text(
                          title!,
                          style: textStyle(GPTypography.bodySmall)
                              ?.mergeColor(GPColor.contentSecondary),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      if (department?.isNotEmpty == true)
                        Text(
                          department!,
                          style: textStyle(GPTypography.bodySmall)
                              ?.mergeColor(GPColor.contentTertiary),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: GPColor.lineTertiary,
          indent: 68,
        ),
      ],
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    if (searchStr.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_MAGNIFYINGGLASS_SEARCH_PNG,
              color: Colors.grey,
              width: 48,
              height: 48,
            ),
            const SizedBox(height: 16),
            Text(
              LocaleKeys.error_nodata.tr,
              style: textStyle(GPTypography.bodyMedium)
                  ?.mergeColor(GPColor.contentSecondary),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Text(
        LocaleKeys.error_nodata.tr,
        style: textStyle(GPTypography.bodyMedium)
            ?.mergeColor(GPColor.contentSecondary),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GPBaseListBlocV2, BaseListState>(
      bloc: bloc,
      builder: (context, state) {
        if (state is BaseListDataLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is BaseListDataLoaded<Assignee>) {
          final users = state.data ?? [];

          if (users.isEmpty) {
            return _buildEmptyWidget(context);
          }

          return ListView.builder(
            itemCount: users.length,
            itemBuilder: (context, index) {
              return _buildItem(context, users[index], index);
            },
          );
        } else if (state is BaseListHasError) {
          return Center(
            child: Text(
              'Error: ${state.errror}',
              style: textStyle(GPTypography.bodyMedium)
                  ?.mergeColor(GPColor.contentSecondary),
            ),
          );
        }

        return _buildEmptyWidget(context);
      },
    );
  }
}
