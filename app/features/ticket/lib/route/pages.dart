import 'package:gp_core/core.dart';
import 'package:gp_core/utils/dio_wrapper.dart';

import '../presentation/create/ticket_create.page.dart';
import '../presentation/details/additional_request/edit/additional_request_edit.page.dart';
import '../presentation/details/edit/detail_edit.page.dart';
import '../presentation/details/ticket_details.page.dart';
import '../presentation/main/ticket.main.page.dart';
import '../presentation/user_list/user_list_page.dart';
import 'arguments/page_arguments.dart';
import 'router_name.dart';

mixin TicketPages {
  static List<GetPage> pages = [
    GetPage(
      name: RouterName.routeWithoutAnimation(TicketRoutes.ticketMain),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: GPTicketMainPage()),
    ),
    GetPage(
      name: TicketRoutes.ticketMain,
      page: () => const GPTicketMainPage(),
    ),
    GetPage(
      name: TicketRoutes.ticketDetails,
      page: () {
        final args = Get.arguments as TicketDetailsArguments;
        return TicketDetailsPage(
          id: args.id,
          entity: args.entity,
        );
      },
    ),
    GetPage(
      name: TicketRoutes.ticketCreate,
      page: () {
        final args = Get.arguments as TicketCreateArguments?;
        return TicketCreatePage(
          mode: args?.mode ?? TicketCreateMode.create,
          ticketListResponse: args?.ticketListResponse,
        );
      },
    ),
    GetPage(
      name: TicketRoutes.ticketEdit,
      page: () {
        final args = Get.arguments as TicketEditArguments;
        return TicketDetailsEditPage(
          ticketListResponse: args.ticketListResponse,
          permissions: args.permissions,
          nodeEntity: args.nodeEntity,
        );
      },
    ),
    GetPage(
      name: TicketRoutes.ticketAdditionalRequestEdit,
      page: () {
        final args = Get.arguments as TicketAdditionalRequestEditArguments;
        return TicketAdditionalRequestEditPage(
          ticketListResponse: args.ticketListResponse,
          additionalRequestResponse: args.additionalRequestResponse,
          fieldPermissions: args.fieldPermissions,
          ticketNodeEntity: args.ticketNodeEntity,
        );
      },
    ),
    GetPage(
      name: TicketRoutes.userList,
      page: () {
        final args = Get.arguments as UserListArguments?;
        return UserListPage(
          title: args?.title,
          filterOutMemberIds: args?.filterOutMemberIds ?? [],
          ignoreMe: args?.ignoreMe ?? false,
          onUserSelected: args?.onUserSelected,
        );
      },
    ),
  ];
}
