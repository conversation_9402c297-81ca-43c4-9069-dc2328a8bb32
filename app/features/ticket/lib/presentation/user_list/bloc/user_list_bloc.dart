import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/presentation/base/base.dart';

class UserListBloc
    extends GPBaseListBlocV2<Assignee, ListAPIResponse<Assignee>, String>
    with BaseListBehaviorMixin<Assignee, ListAPIResponse<Assignee>, String>
    implements BaseListBehavior<Assignee, ListAPIResponse<Assignee>, String> {
  UserListBloc() {
    setOnUseCaseBehavior(this);
  }

  final AssigneeApi _assigneeApi = AssigneeApi();
  List<int> filterOutMemberIds = [];
  bool ignoreMe = false;

  void setFilterOptions({
    List<int>? filterOutMemberIds,
    bool? ignoreMe,
  }) {
    this.filterOutMemberIds = filterOutMemberIds ?? [];
    this.ignoreMe = ignoreMe ?? false;
  }

  @override
  Future<ListAPIResponse<Assignee>?> usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    final searchText = inputData as String? ?? '';

    return await _assigneeApi.getAssignees(
      q: searchText,
      onlyCurrentWorkspace: true,
      nextLink: nextLink ?? '',
      limit: 30,
      isSearching: searchText.isNotEmpty,
      groupId: '',
      threadId: '',
    );
  }

  @override
  Future<ListAPIResponse<Assignee>?> usecaseOnSearchData({
    required String params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    return await _assigneeApi.getAssignees(
      q: params,
      onlyCurrentWorkspace: true,
      nextLink: nextLink ?? '',
      limit: 30,
      isSearching: params.isNotEmpty,
      groupId: '',
      threadId: '',
    );
  }

  @override
  Future<BaseListDataLoaded<Assignee>> emitData({
    ListAPIResponse<Assignee>? response,
    List<Assignee>? entityData,
    required Emitter<BaseListState> emit,
    required bool isInitialLoad,
  }) async {
    var entities = entityData ?? response?.data ?? [];

    // Filter out members if needed
    if (filterOutMemberIds.isNotEmpty) {
      entities = entities
          .where((assignee) => !filterOutMemberIds.contains(assignee.id))
          .toList();
    }

    // Filter out current user if needed
    if (ignoreMe) {
      entities = entities
          .where((assignee) => assignee.id.toString() != Constants.userId())
          .toList();
    }

    final state = BaseListDataLoaded<Assignee>(
      data: entities,
      nextLink: response?.links?.next,
      canNextPage: response?.links?.canNextPage() ?? false,
      isInitialLoad: isInitialLoad,
    );

    emit(state);
    return state;
  }
}
